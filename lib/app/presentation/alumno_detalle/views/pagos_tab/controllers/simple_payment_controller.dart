import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:get_it/get_it.dart';
import '../../../../../domain/entities/alumno.dart';
import '../../../../../domain/modelos/kardex.dart';
import '../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../domain/modelos/detalle_pago_unificado.dart';
import '../../../../../domain/modelos/recibo.dart';
import '../../../../../domain/repositories/kardex_repository.dart';
import '../../../../../domain/repositories/recibo_repository.dart';
import '../widgets/crear_recibo_mejorado/payment_distribution_engine.dart';

class SimplePaymentController extends GetxController {
  // Datos del alumno
  late final Alumno alumno;
  late final VoidCallback? onReciboCreated;

  // Estado reactivo
  final isLoading = true.obs;
  final semanas = <KardexSemana>[].obs;
  final costosAdicionales = <CostoAdicional>[].obs;
  final montoPorSemana = 0.0.obs;

  // Formulario
  final formKey = GlobalKey<FormState>();
  final montoController = TextEditingController();
  final notasController = TextEditingController();

  // Estado del pago
  final currentDistribution = Rxn<PaymentDistribution>();
  final metodoPago = 'Efectivo'.obs;
  final costosSeleccionados = <String>{}.obs;
  final costoControllers = <String, TextEditingController>{}.obs;
  final costosMontos = <String, double>{}.obs;

  final metodosPago = [
    'Efectivo',
    'Transferencia',
    'Tarjeta de Débito',
    'Tarjeta de Crédito',
    'Cheque'
  ];

  @override
  void onInit() {
    super.onInit();
    montoController.addListener(_onAmountChanged);
    _initializeCostoControllers();
  }

  @override
  void onClose() {
    montoController.dispose();
    notasController.dispose();
    for (final controller in costoControllers.values) {
      controller.dispose();
    }
    super.onClose();
  }

  void initialize(Alumno alumno, {VoidCallback? onReciboCreated}) {
    this.alumno = alumno;
    this.onReciboCreated = onReciboCreated;
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      isLoading.value = true;
      final kardexRepo = GetIt.instance<KardexRepository>();

      // Load kardex data
      final kardex = await kardexRepo.getKardexByAlumnoId(alumno.id);
      final semanasData = await kardexRepo.getSemanas(alumno.id);
      final costosData = await kardexRepo.getCostosAdicionales(alumno.id);

      montoPorSemana.value = kardex.montoPorSemana;
      semanas.value = semanasData;
      costosAdicionales.value = costosData;
      
      _initializeCostoControllers();
      isLoading.value = false;
    } catch (e) {
      debugPrint('Error loading data: $e');
      isLoading.value = false;
    }
  }

  void _initializeCostoControllers() {
    costoControllers.clear();
    costosMontos.clear();
    
    for (final costo in costosAdicionales) {
      final controller = TextEditingController();
      controller.addListener(() => _onCostoAmountChanged(costo.id));
      costoControllers[costo.id] = controller;
      costosMontos[costo.id] = 0.0;
    }
  }

  void _onAmountChanged() {
    final amount = double.tryParse(montoController.text) ?? 0;
    if (amount > 0) {
      _calculateDistribution();
    } else {
      currentDistribution.value = null;
    }
  }

  void _calculateDistribution() {
    final amount = double.tryParse(montoController.text) ?? 0;
    if (amount <= 0) return;

    _calculateManualDistribution(amount);
  }

  void _calculateManualDistribution(double totalAmount) {
    final costDistributions = <CostDistribution>[];
    final weekDistributions = <WeekDistribution>[];

    double remainingAmount = totalAmount;

    // 1. Primero, asignar los costos adicionales con sus montos específicos
    for (final costoId in costosSeleccionados) {
      final costo = costosAdicionales.firstWhere((c) => c.id == costoId);
      final assignedAmount = costosMontos[costoId] ?? 0.0;

      if (assignedAmount > 0 && remainingAmount >= assignedAmount) {
        costDistributions.add(CostDistribution(
          costoAdicional: costo,
          amount: assignedAmount,
          pendingBefore: costo.monto - costo.montoPagado,
          pendingAfter: (costo.monto - costo.montoPagado) - assignedAmount,
          isFullyPaid: assignedAmount >= (costo.monto - costo.montoPagado),
        ));
        remainingAmount -= assignedAmount;
      }
    }

    // 2. Luego, distribuir el resto a las semanas
    if (remainingAmount > 0) {
      // Primero semanas pendientes
      final semanasPendientes = semanas
          .where((semana) => !semana.pagada)
          .toList()
        ..sort((a, b) => a.numeroSemana.compareTo(b.numeroSemana));

      for (final semana in semanasPendientes) {
        if (remainingAmount <= 0) break;

        final pendingAmount = semana.monto - semana.montoPagado;
        final assignedAmount = remainingAmount >= pendingAmount
            ? pendingAmount
            : remainingAmount;

        weekDistributions.add(WeekDistribution(
          numeroSemana: semana.numeroSemana,
          amount: assignedAmount,
          weeklyAmount: semana.monto,
          isExisting: true,
          isFullyPaid: assignedAmount >= pendingAmount,
          pendingBefore: pendingAmount,
          pendingAfter: pendingAmount - assignedAmount,
        ));

        remainingAmount -= assignedAmount;
      }

      // Luego semanas nuevas
      if (remainingAmount > 0) {
        final maxWeek = semanas.isNotEmpty
            ? semanas.map((s) => s.numeroSemana).reduce((a, b) => a > b ? a : b)
            : 0;

        int weekNumber = maxWeek + 1;
        while (remainingAmount >= montoPorSemana.value) {
          weekDistributions.add(WeekDistribution(
            numeroSemana: weekNumber,
            amount: montoPorSemana.value,
            weeklyAmount: montoPorSemana.value,
            isExisting: false,
            isFullyPaid: true,
            pendingBefore: montoPorSemana.value,
            pendingAfter: 0,
          ));

          remainingAmount -= montoPorSemana.value;
          weekNumber++;
        }
      }
    }

    final distribution = PaymentDistribution(
      totalAmount: totalAmount,
      costDistributions: costDistributions,
      weekDistributions: weekDistributions,
      remainingAmount: remainingAmount,
      isComplete: remainingAmount <= 0.01,
    );

    currentDistribution.value = distribution;
  }

  void onCostoToggled(String costoId, bool isSelected) {
    if (isSelected) {
      costosSeleccionados.add(costoId);
      // Inicializar con el monto pendiente completo
      final costo = costosAdicionales.firstWhere((c) => c.id == costoId);
      final pendiente = costo.monto - costo.montoPagado;
      costosMontos[costoId] = pendiente;
      costoControllers[costoId]?.text = pendiente.toStringAsFixed(2);
    } else {
      costosSeleccionados.remove(costoId);
      costosMontos[costoId] = 0.0;
      costoControllers[costoId]?.clear();
    }
    _calculateDistribution();
  }

  void _onCostoAmountChanged(String costoId) {
    final text = costoControllers[costoId]?.text ?? '';
    final amount = double.tryParse(text) ?? 0.0;

    // Obtener el monto máximo permitido
    final costo = costosAdicionales.firstWhere((c) => c.id == costoId);
    final maxAmount = costo.monto - costo.montoPagado;

    // Limitar el monto al máximo permitido
    final finalAmount = amount.clamp(0.0, maxAmount);

    costosMontos[costoId] = finalAmount;
    _calculateDistribution();
  }

  double calculateTotalPendiente() {
    double total = 0;

    // Sumar semanas pendientes
    for (final semana in semanas) {
      if (!semana.pagada) {
        total += semana.monto - semana.montoPagado;
      }
    }

    // Sumar costos adicionales pendientes
    for (final costo in costosAdicionales) {
      if (!costo.pagado) {
        total += costo.monto - costo.montoPagado;
      }
    }

    return total;
  }

  void setQuickAmount(double amount) {
    montoController.text = amount.toStringAsFixed(2);
  }

  void setCostoAmount(String costoId, double amount) {
    costoControllers[costoId]?.text = amount.toStringAsFixed(2);
    _onCostoAmountChanged(costoId);
  }

  Future<void> generateRecibo() async {
    if (!formKey.currentState!.validate()) return;
    if (currentDistribution.value == null) return;

    final amount = double.tryParse(montoController.text) ?? 0;
    if (amount <= 0) return;

    try {
      // Crear los detalles del pago
      final detalles = currentDistribution.value!.toDetallesPago(alumno.id);

      // Crear el concepto del recibo
      String concepto = 'Pago de ';
      final conceptos = <String>[];

      if (currentDistribution.value!.weekDistributions.isNotEmpty) {
        final semanas = currentDistribution.value!.weekDistributions.length;
        conceptos.add('$semanas semana${semanas > 1 ? 's' : ''}');
      }

      if (currentDistribution.value!.costDistributions.isNotEmpty) {
        final costos = currentDistribution.value!.costDistributions.length;
        conceptos.add('$costos costo${costos > 1 ? 's' : ''} adicional${costos > 1 ? 'es' : ''}');
      }

      concepto += conceptos.join(' y ');

      // Agregar notas si las hay
      final notas = notasController.text.trim();
      if (notas.isNotEmpty) {
        concepto += ' - $notas';
      }

      final kardexRepo = GetIt.instance<KardexRepository>();
      final reciboRepo = GetIt.instance<ReciboRepository>();

      // Generate reciboId for all payment details
      final reciboId = 'REC-${DateTime.now().millisecondsSinceEpoch}';

      // Save each payment detail using KardexRepository
      for (final detalle in detalles) {
        final newDetalle = DetallePagoUnificado(
          id: 'DET-PAGO-${DateTime.now().millisecondsSinceEpoch}-${detalles.indexOf(detalle)}',
          tipo: detalle.tipo,
          concepto: detalle.concepto,
          referenciaId: detalle.referenciaId,
          monto: detalle.monto,
          fecha: detalle.fecha,
          alumnoId: alumno.id,
          reciboId: reciboId,
          numeroSemana: detalle.numeroSemana,
        );
        await kardexRepo.guardarDetallePago(newDetalle);
      }

      // Create receipt using ReciboRepository
      final recibo = Recibo(
        id: reciboId,
        folio: Recibo.generarFolio(),
        alumnoId: alumno.id,
        alumnoNombre: alumno.nombre,
        fechaEmision: DateTime.now(),
        montoTotal: amount,
        metodoPago: metodoPago.value,
        concepto: concepto,
        detalles: detalles,
      );
      await reciboRepo.crearRecibo(recibo.toMap());

      // Update kardex semanas
      await _updateKardexSemanas(currentDistribution.value!, reciboId, kardexRepo);

      // Update additional costs
      await _updateCostosAdicionales(currentDistribution.value!, reciboId, kardexRepo);

      // Update main kardex
      await _updateMainKardex(amount, kardexRepo);

      // Mostrar mensaje de éxito
      Get.snackbar(
        'Éxito',
        'Recibo generado exitosamente',
        backgroundColor: Colors.green,
        colorText: Colors.white,
        duration: const Duration(seconds: 3),
      );

      // Limpiar formulario
      _clearForm();

      // Cambiar al tab de información
      onReciboCreated?.call();

    } catch (e) {
      debugPrint('Error generating receipt: $e');
      Get.snackbar(
        'Error',
        'Error al generar recibo: ${e.toString()}',
        backgroundColor: Colors.red,
        colorText: Colors.white,
        duration: const Duration(seconds: 5),
      );
      rethrow;
    }
  }

  void _clearForm() {
    montoController.clear();
    notasController.clear();
    currentDistribution.value = null;
    costosSeleccionados.clear();
    costosMontos.clear();
    for (final controller in costoControllers.values) {
      controller.clear();
    }
  }

  Future<void> _updateKardexSemanas(
    PaymentDistribution distribution,
    String reciboId,
    KardexRepository kardexRepo,
  ) async {
    for (final weekDist in distribution.weekDistributions) {
      final semanaId = weekDist.numeroSemana.toString();
      KardexSemana? semana;

      try {
        semana = await kardexRepo.getKardexSemanaById(alumno.id, semanaId);
      } catch (e) {
        // Not found, we'll create a new one
      }

      if (semana != null) {
        // Update existing semana
        final newMontoPagado = semana.montoPagado + weekDist.amount;
        final isPaid = newMontoPagado >= semana.monto;

        final updatedSemana = semana.copyWith(
          montoPagado: newMontoPagado,
          estado: isPaid ? 'pagado' : 'pendiente',
          pagada: isPaid,
          updatedAt: DateTime.now(),
          reciboIds: [...semana.reciboIds, reciboId],
        );

        await kardexRepo.updateKardexSemana(updatedSemana);
      } else {
        // Create new semana
        final montoPorSemanaValue = montoPorSemana.value > 0 ? montoPorSemana.value : weekDist.weeklyAmount;
        final isPaid = weekDist.amount >= montoPorSemanaValue;
        semana = KardexSemana(
          reciboIds: [reciboId],
          id: semanaId,
          kardexId: alumno.id,
          numeroSemana: weekDist.numeroSemana,
          monto: montoPorSemanaValue,
          montoPagado: weekDist.amount,
          estado: isPaid ? 'pagado' : 'pendiente',
          pagada: isPaid,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
        await kardexRepo.updateKardexSemana(semana);
      }
    }
  }

  Future<void> _updateCostosAdicionales(
    PaymentDistribution distribution,
    String reciboId,
    KardexRepository kardexRepo,
  ) async {
    for (final costDist in distribution.costDistributions) {
      final costo = costDist.costoAdicional;
      final nuevoMontoPagado = (costo.montoPagado + costDist.amount).clamp(0, costo.monto);
      final estaPagado = nuevoMontoPagado >= costo.monto;

      try {
        final updatedCosto = CostoAdicional(
          id: costo.id,
          kardexId: costo.kardexId,
          nombre: costo.nombre,
          descripcion: costo.descripcion,
          monto: costo.monto,
          montoPagado: nuevoMontoPagado.toDouble(),
          pagado: estaPagado,
          reciboId: reciboId,
          fechaVencimiento: costo.fechaVencimiento,
          createdAt: costo.createdAt,
          updatedAt: DateTime.now(),
        );

        await kardexRepo.actualizarCostoAdicional(updatedCosto);
      } catch (e) {
        debugPrint('Error updating cost ${costo.nombre}: $e');
        rethrow;
      }
    }
  }

  Future<void> _updateMainKardex(double montoTotal, KardexRepository kardexRepo) async {
    try {
      final kardex = await kardexRepo.getKardexByAlumnoId(alumno.id);
      final nuevoTotalPagado = kardex.totalPagado + montoTotal;
      final nuevoSaldoPendiente = kardex.saldoPendiente - montoTotal;

      await kardexRepo.updateKardex(
        kardex.copyWith(
          totalPagado: nuevoTotalPagado,
          saldoPendiente: nuevoSaldoPendiente,
          updatedAt: DateTime.now(),
        ),
      );
    } catch (e) {
      debugPrint('Error updating kardex: $e');
      rethrow;
    }
  }
}
