import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../../styles/app_styles.dart';
import '../../../controllers/simple_payment_controller.dart';

class GenerateButtonWidget extends StatelessWidget {
  const GenerateButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SimplePaymentController>();

    return Obx(() {
      final canGenerate = controller.formKey.currentState?.validate() ?? false;
      final hasAmount = (double.tryParse(controller.montoController.text) ?? 0) > 0;
      final hasDistribution = controller.currentDistribution.value != null &&
                             controller.currentDistribution.value!.totalDistributed > 0;

      return SizedBox(
        width: double.infinity,
        height: 56,
        child: ElevatedButton(
          onPressed: (canGenerate && hasAmount && hasDistribution)
              ? () => controller.generateRecibo()
              : null,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppStyles.primaryColor,
            foregroundColor: Colors.white,
            elevation: 2,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            disabledBackgroundColor: Colors.grey[300],
            disabledForegroundColor: Colors.grey[600],
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Icon(Icons.receipt_long, size: 24),
              const SizedBox(width: 12),
              Text(
                'Generar Recibo',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      );
    });
  }
}
