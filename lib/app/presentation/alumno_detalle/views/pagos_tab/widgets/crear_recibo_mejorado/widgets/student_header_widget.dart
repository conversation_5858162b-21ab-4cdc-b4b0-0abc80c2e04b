import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../../domain/entities/alumno.dart';
import '../../../../../../../styles/app_styles.dart';

class StudentHeaderWidget extends StatelessWidget {
  final Alumno alumno;

  const StudentHeaderWidget({
    super.key,
    required this.alumno,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              Icons.person,
              color: AppStyles.primaryColor,
              size: 30,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  alumno.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Nuevo Recibo de Pago',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.receipt_long,
            color: AppStyles.primaryColor,
            size: 28,
          ),
        ],
      ),
    );
  }
}
