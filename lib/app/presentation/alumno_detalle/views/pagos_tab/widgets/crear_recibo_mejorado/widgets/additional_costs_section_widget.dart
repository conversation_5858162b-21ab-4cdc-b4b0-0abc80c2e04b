import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../../styles/app_styles.dart';
import '../../../controllers/simple_payment_controller.dart';

class AdditionalCostsSectionWidget extends StatelessWidget {
  const AdditionalCostsSectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SimplePaymentController>();

    return Obx(() {
      final costosPendientes = controller.costosAdicionales
          .where((costo) => !costo.pagado)
          .toList();

      if (costosPendientes.isEmpty) {
        return const SizedBox.shrink();
      }

      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.add_circle_outline,
                  color: AppStyles.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Costos Adicionales',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Selecciona los costos que deseas pagar primero',
              style: GoogleFonts.poppins(
                fontSize: 14,
                color: AppStyles.secondaryTextColor,
              ),
            ),
            const SizedBox(height: 16),
            ...costosPendientes.map((costo) => _buildCostoToggle(costo)),
          ],
        ),
      );
    });
  }

  Widget _buildCostoToggle(CostoAdicional costo) {
    final controller = Get.find<SimplePaymentController>();
    final pendiente = costo.monto - costo.montoPagado;

    return Obx(() {
      final isSelected = controller.costosSeleccionados.contains(costo.id);

      return Container(
        margin: const EdgeInsets.only(bottom: 12),
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: isSelected ? AppStyles.primaryColor.withValues(alpha: 0.1) : Colors.grey[50],
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected ? AppStyles.primaryColor : Colors.grey[300]!,
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Checkbox(
                  value: isSelected,
                  onChanged: (value) => controller.onCostoToggled(costo.id, value ?? false),
                  activeColor: AppStyles.primaryColor,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        costo.nombre,
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppStyles.textColor,
                        ),
                      ),
                      if (costo.descripcion.isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          costo.descripcion,
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            color: AppStyles.secondaryTextColor,
                          ),
                        ),
                      ],
                      const SizedBox(height: 8),
                      Text(
                        'Pendiente: \$${pendiente.toStringAsFixed(2)}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.orange[700],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            // Input de monto cuando está seleccionado
            if (isSelected) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Monto a abonar:',
                          style: GoogleFonts.poppins(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: AppStyles.textColor,
                          ),
                        ),
                        const SizedBox(height: 8),
                        TextFormField(
                          controller: controller.costoControllers[costo.id],
                          keyboardType: const TextInputType.numberWithOptions(decimal: true),
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                          ],
                          style: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppStyles.textColor,
                          ),
                          decoration: InputDecoration(
                            prefixText: '\$ ',
                            prefixStyle: GoogleFonts.poppins(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                              color: AppStyles.primaryColor,
                            ),
                            hintText: '0.00',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 12),
                  // Botones de monto rápido
                  Column(
                    children: [
                      const SizedBox(height: 20),
                      _buildQuickAmountButton(
                        label: 'Todo',
                        onTap: () => controller.setCostoAmount(costo.id, pendiente),
                      ),
                      const SizedBox(height: 8),
                      _buildQuickAmountButton(
                        label: '50%',
                        onTap: () => controller.setCostoAmount(costo.id, pendiente * 0.5),
                      ),
                    ],
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                'Máximo: \$${pendiente.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 12,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildQuickAmountButton({
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppStyles.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppStyles.primaryColor.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppStyles.primaryColor,
          ),
        ),
      ),
    );
  }
}
