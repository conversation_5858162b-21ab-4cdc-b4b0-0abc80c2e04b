import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../../styles/app_styles.dart';
import '../../../controllers/simple_payment_controller.dart';

class AmountSectionWidget extends StatelessWidget {
  const AmountSectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SimplePaymentController>();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payments,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Monto a Pagar',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: controller.montoController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
            decoration: InputDecoration(
              prefixText: '\$ ',
              prefixStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppStyles.primaryColor,
              ),
              hintText: '0.00',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Ingrese un monto válido';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'El monto debe ser mayor a cero';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Obx(() {
            final totalPendiente = controller.calculateTotalPendiente();
            return Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppStyles.primaryColor.withValues(alpha: 0.05),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Total Pendiente:',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      color: AppStyles.secondaryTextColor,
                    ),
                  ),
                  Text(
                    '\$${totalPendiente.toStringAsFixed(2)}',
                    style: GoogleFonts.poppins(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: AppStyles.primaryColor,
                    ),
                  ),
                ],
              ),
            );
          }),
          const SizedBox(height: 12),
          _buildQuickSuggestions(),
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    final controller = Get.find<SimplePaymentController>();
    
    return Obx(() {
      final suggestions = <double>[];

      // Próxima semana
      if (controller.montoPorSemana.value > 0) {
        suggestions.add(controller.montoPorSemana.value);
        suggestions.add(controller.montoPorSemana.value * 2);
        suggestions.add(controller.montoPorSemana.value * 4);
      }

      // Montos redondos
      suggestions.addAll([500.0, 1000.0, 2000.0]);

      return Wrap(
        spacing: 8,
        runSpacing: 8,
        children: suggestions.take(6).map((amount) {
          String label = '\$${amount.toStringAsFixed(0)}';
          if (amount == controller.montoPorSemana.value) {
            label += ' (1 sem)';
          } else if (amount == controller.montoPorSemana.value * 2) {
            label += ' (2 sem)';
          } else if (amount == controller.montoPorSemana.value * 4) {
            label += ' (4 sem)';
          }

          return GestureDetector(
            onTap: () => controller.setQuickAmount(amount),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: AppStyles.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                label,
                style: GoogleFonts.poppins(
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                  color: AppStyles.primaryColor,
                ),
              ),
            ),
          );
        }).toList(),
      );
    });
  }
}
