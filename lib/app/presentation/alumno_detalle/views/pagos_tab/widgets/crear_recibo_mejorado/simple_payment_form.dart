import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../styles/app_styles.dart';
import '../../controllers/simple_payment_controller.dart';
import 'widgets/student_header_widget.dart';
import 'widgets/amount_section_widget.dart';
import 'widgets/payment_method_section_widget.dart';
import 'widgets/additional_costs_section_widget.dart';
import 'widgets/distribution_section_widget.dart';
import 'widgets/notes_section_widget.dart';
import 'widgets/generate_button_widget.dart';

class SimplePaymentForm extends StatelessWidget {
  final Alumno alumno;
  final VoidCallback? onReciboCreated;

  const SimplePaymentForm({
    super.key,
    required this.alumno,
    this.onReciboCreated,
  });

  @override
  Widget build(BuildContext context) {
    // Inicializar el controlador
    final controller = Get.put(SimplePaymentController(), tag: alumno.id);
    controller.initialize(alumno, onReciboCreated: onReciboCreated);

    return Scaffold(
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header con información del alumno
              StudentHeaderWidget(alumno: alumno),

              const SizedBox(height: 24),

              // Formulario de pago
              Expanded(
                child: Form(
                  key: controller.formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AmountSectionWidget(),
                        const SizedBox(height: 24),
                        PaymentMethodSectionWidget(),
                        const SizedBox(height: 24),
                        Obx(() {
                          if (controller.costosAdicionales.isNotEmpty) {
                            return Column(
                              children: [
                                AdditionalCostsSectionWidget(),
                                const SizedBox(height: 24),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        }),
                        Obx(() {
                          if (controller.currentDistribution.value != null) {
                            return Column(
                              children: [
                                DistributionSectionWidget(),
                                const SizedBox(height: 24),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        }),
                        NotesSectionWidget(),
                        const SizedBox(height: 32),
                        GenerateButtonWidget(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}






  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Método de Pago',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _metodoPago,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
            items: _metodosPago.map((metodo) {
              return DropdownMenuItem<String>(
                value: metodo,
                child: Text(metodo),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _metodoPago = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCostosAdicionalesSection() {
    final costosPendientes = widget.costosAdicionales
        .where((costo) => !costo.pagado)
        .toList();

    if (costosPendientes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.add_circle_outline,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Costos Adicionales',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Selecciona los costos que deseas pagar primero',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...costosPendientes.map((costo) => _buildCostoToggle(costo)),
        ],
      ),
    );
  }

  Widget _buildCostoToggle(CostoAdicional costo) {
    final pendiente = costo.monto - costo.montoPagado;
    final isSelected = _costosSeleccionados.contains(costo.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected ? AppStyles.primaryColor.withValues(alpha: 0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppStyles.primaryColor : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Checkbox(
                value: isSelected,
                onChanged: (value) => _onCostoToggled(costo.id, value ?? false),
                activeColor: AppStyles.primaryColor,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      costo.nombre,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppStyles.textColor,
                      ),
                    ),
                    if (costo.descripcion.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        costo.descripcion,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: AppStyles.secondaryTextColor,
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Text(
                      'Pendiente: \$${pendiente.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Input de monto cuando está seleccionado
          if (isSelected) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Monto a abonar:',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppStyles.textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _costoControllers[costo.id],
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppStyles.textColor,
                        ),
                        decoration: InputDecoration(
                          prefixText: '\$ ',
                          prefixStyle: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppStyles.primaryColor,
                          ),
                          hintText: '0.00',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Botones de monto rápido
                Column(
                  children: [
                    const SizedBox(height: 20),
                    _buildQuickAmountButton(
                      label: 'Todo',
                      onTap: () {
                        _costoControllers[costo.id]?.text = pendiente.toStringAsFixed(2);
                        _onCostoAmountChanged(costo.id);
                      },
                    ),
                    const SizedBox(height: 8),
                    _buildQuickAmountButton(
                      label: '50%',
                      onTap: () {
                        final amount = pendiente * 0.5;
                        _costoControllers[costo.id]?.text = amount.toStringAsFixed(2);
                        _onCostoAmountChanged(costo.id);
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Máximo: \$${pendiente.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppStyles.secondaryTextColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickAmountButton({
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppStyles.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppStyles.primaryColor.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppStyles.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildDistributionSection() {
    if (_currentDistribution == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Distribución del Pago',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Resumen
          _buildDistributionSummary(),

          const SizedBox(height: 16),

          // Detalles de costos adicionales
          if (_currentDistribution!.costDistributions.isNotEmpty) ...[
            _buildCostDistributionDetails(),
            const SizedBox(height: 16),
          ],

          // Detalles de semanas
          if (_currentDistribution!.weekDistributions.isNotEmpty) ...[
            _buildWeekDistributionDetails(),
          ],
        ],
      ),
    );
  }

  Widget _buildDistributionSummary() {
    final distribution = _currentDistribution!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppStyles.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Monto Total:',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
              Text(
                '\$${distribution.totalAmount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: AppStyles.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distribuido:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
              Text(
                '\$${distribution.totalDistributed.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          if (distribution.remainingAmount > 0.01) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Restante:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${distribution.remainingAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCostDistributionDetails() {
    final costDistributions = _currentDistribution!.costDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Costos Adicionales (${costDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...costDistributions.map((cost) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                cost.isFullyPaid ? Icons.check_circle : Icons.schedule,
                size: 16,
                color: cost.isFullyPaid ? Colors.green[700] : Colors.blue[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  cost.costoAdicional.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                '\$${cost.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildWeekDistributionDetails() {
    final weekDistributions = _currentDistribution!.weekDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas (${weekDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: weekDistributions.map((week) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: week.isExisting ? Colors.orange[100] : Colors.green[100],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: week.isExisting ? Colors.orange[300]! : Colors.green[300]!,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  week.isExisting ? Icons.schedule : Icons.add_circle,
                  size: 14,
                  color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                ),
                const SizedBox(width: 4),
                Text(
                  'S${week.numeroSemana}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '\$${week.amount.toStringAsFixed(0)}',
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: week.isExisting ? Colors.orange[600] : Colors.green[600],
                  ),
                ),
              ],
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_add,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Notas (Opcional)',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notasController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Agregar notas o comentarios sobre este pago...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateButton() {
    final canGenerate = _formKey.currentState?.validate() ?? false;
    final hasAmount = (double.tryParse(_montoController.text) ?? 0) > 0;
    final hasDistribution = _currentDistribution != null &&
                           _currentDistribution!.totalDistributed > 0;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: (canGenerate && hasAmount && hasDistribution)
            ? _generateRecibo
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppStyles.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          disabledBackgroundColor: Colors.grey[300],
          disabledForegroundColor: Colors.grey[600],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.receipt_long, size: 24),
            const SizedBox(width: 12),
            Text(
              'Generar Recibo',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _generateRecibo() {
    if (!_formKey.currentState!.validate()) return;
    if (_currentDistribution == null) return;

    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    // Crear los detalles del pago
    final detalles = _currentDistribution!.toDetallesPago(widget.alumno.id);

    // Crear el concepto del recibo
    String concepto = 'Pago de ';
    final conceptos = <String>[];

    if (_currentDistribution!.weekDistributions.isNotEmpty) {
      final semanas = _currentDistribution!.weekDistributions.length;
      conceptos.add('$semanas semana${semanas > 1 ? 's' : ''}');
    }

    if (_currentDistribution!.costDistributions.isNotEmpty) {
      final costos = _currentDistribution!.costDistributions.length;
      conceptos.add('$costos costo${costos > 1 ? 's' : ''} adicional${costos > 1 ? 'es' : ''}');
    }

    concepto += conceptos.join(' y ');

    // Agregar notas si las hay
    final notas = _notasController.text.trim();
    if (notas.isNotEmpty) {
      concepto += ' - $notas';
    }

    // Preparar los datos para el callback
    final reciboData = {
      'alumno': widget.alumno,
      'monto': amount,
      'metodoPago': _metodoPago,
      'concepto': concepto,
      'detalles': detalles,
      'distribution': _currentDistribution,
      'notas': notas,
    };

    // Llamar al callback
    widget.onGenerarRecibo(reciboData);
  }
}
