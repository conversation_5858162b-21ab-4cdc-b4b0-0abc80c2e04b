import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../domain/modelos/kardex.dart';
import '../../../../../../domain/modelos/costo_adicional.dart';
import '../../../../../../styles/app_styles.dart';
import 'payment_distribution_engine.dart';

class SimplePaymentForm extends StatefulWidget {
  final Alumno alumno;
  final List<CostoAdicional> costosAdicionales;
  final List<KardexSemana> semanas;
  final double montoPorSemana;
  final Function(Map<String, dynamic>) onGenerarRecibo;

  const SimplePaymentForm({
    super.key,
    required this.alumno,
    required this.costosAdicionales,
    required this.semanas,
    required this.montoPorSemana,
    required this.onGenerarRecibo,
  });

  @override
  State<SimplePaymentForm> createState() => _SimplePaymentFormState();
}

class _SimplePaymentFormState extends State<SimplePaymentForm> {
  final _formKey = GlobalKey<FormState>();
  final _montoController = TextEditingController();
  final _notasController = TextEditingController();

  PaymentDistribution? _currentDistribution;
  String _metodoPago = 'Efectivo';
  final Set<String> _costosSeleccionados = {};
  final Map<String, TextEditingController> _costoControllers = {};
  final Map<String, double> _costosMontos = {};

  final List<String> _metodosPago = [
    'Efectivo',
    'Transferencia',
    'Tarjeta de Débito',
    'Tarjeta de Crédito',
    'Cheque'
  ];

  @override
  void initState() {
    super.initState();
    _montoController.addListener(_onAmountChanged);
    _initializeCostoControllers();
  }

  void _initializeCostoControllers() {
    for (final costo in widget.costosAdicionales) {
      final controller = TextEditingController();
      //detalle
      controller.addListener(() => _onCostoAmountChanged(costo.id));
      _costoControllers[costo.id] = controller;
      _costosMontos[costo.id] = 0.0;
    }
  }

  @override
  void dispose() {
    _montoController.dispose();
    _notasController.dispose();
    for (final controller in _costoControllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _onAmountChanged() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount > 0) {
      _calculateDistribution();
    } else {
      setState(() {
        _currentDistribution = null;
      });
    }
  }

  void _calculateDistribution() {
    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    // Crear una distribución manual basada en los montos específicos
    _calculateManualDistribution(amount);
  }

  void _calculateManualDistribution(double totalAmount) {
    final costDistributions = <CostDistribution>[];
    final weekDistributions = <WeekDistribution>[];

    double remainingAmount = totalAmount;

    // 1. Primero, asignar los costos adicionales con sus montos específicos
    for (final costoId in _costosSeleccionados) {
      final costo = widget.costosAdicionales.firstWhere((c) => c.id == costoId);
      final assignedAmount = _costosMontos[costoId] ?? 0.0;

      if (assignedAmount > 0 && remainingAmount >= assignedAmount) {
        costDistributions.add(CostDistribution(
          costoAdicional: costo,
          amount: assignedAmount,
          pendingBefore: costo.monto - costo.montoPagado,
          pendingAfter: (costo.monto - costo.montoPagado) - assignedAmount,
          isFullyPaid: assignedAmount >= (costo.monto - costo.montoPagado),
        ));
        remainingAmount -= assignedAmount;
      }
    }

    // 2. Luego, distribuir el resto a las semanas
    if (remainingAmount > 0) {
      // Primero semanas pendientes
      final semanasPendientes = widget.semanas
          .where((semana) => !semana.pagada)
          .toList()
        ..sort((a, b) => a.numeroSemana.compareTo(b.numeroSemana));

      for (final semana in semanasPendientes) {
        if (remainingAmount <= 0) break;

        final pendingAmount = semana.monto - semana.montoPagado;
        final assignedAmount = remainingAmount >= pendingAmount
            ? pendingAmount
            : remainingAmount;

        weekDistributions.add(WeekDistribution(
          numeroSemana: semana.numeroSemana,
          amount: assignedAmount,
          weeklyAmount: semana.monto,
          isExisting: true,
          isFullyPaid: assignedAmount >= pendingAmount,
          pendingBefore: pendingAmount,
          pendingAfter: pendingAmount - assignedAmount,
        ));

        remainingAmount -= assignedAmount;
      }

      // Luego semanas nuevas
      if (remainingAmount > 0) {
        final maxWeek = widget.semanas.isNotEmpty
            ? widget.semanas.map((s) => s.numeroSemana).reduce((a, b) => a > b ? a : b)
            : 0;

        int weekNumber = maxWeek + 1;
        while (remainingAmount >= widget.montoPorSemana) {
          weekDistributions.add(WeekDistribution(
            numeroSemana: weekNumber,
            amount: widget.montoPorSemana,
            weeklyAmount: widget.montoPorSemana,
            isExisting: false,
            isFullyPaid: true,
            pendingBefore: widget.montoPorSemana,
            pendingAfter: 0,
          ));

          remainingAmount -= widget.montoPorSemana;
          weekNumber++;
        }
      }
    }

    final distribution = PaymentDistribution(
      totalAmount: totalAmount,
      costDistributions: costDistributions,
      weekDistributions: weekDistributions,
      remainingAmount: remainingAmount,
      isComplete: remainingAmount <= 0.01,
    );

    setState(() {
      _currentDistribution = distribution;
    });
  }

  void _onCostoToggled(String costoId, bool isSelected) {
    setState(() {
      if (isSelected) {
        _costosSeleccionados.add(costoId);
        // Inicializar con el monto pendiente completo
        final costo = widget.costosAdicionales.firstWhere((c) => c.id == costoId);
        final pendiente = costo.monto - costo.montoPagado;
        _costosMontos[costoId] = pendiente;
        _costoControllers[costoId]?.text = pendiente.toStringAsFixed(2);
      } else {
        _costosSeleccionados.remove(costoId);
        _costosMontos[costoId] = 0.0;
        _costoControllers[costoId]?.clear();
      }
    });
    _calculateDistribution();
  }

  void _onCostoAmountChanged(String costoId) {
    final text = _costoControllers[costoId]?.text ?? '';
    final amount = double.tryParse(text) ?? 0.0;

    // Obtener el monto máximo permitido
    final costo = widget.costosAdicionales.firstWhere((c) => c.id == costoId);
    final maxAmount = costo.monto - costo.montoPagado;

    // Limitar el monto al máximo permitido
    final finalAmount = amount.clamp(0.0, maxAmount);

    setState(() {
      _costosMontos[costoId] = finalAmount;
    });

    _calculateDistribution();
  }

  double _calculateTotalPendiente() {
    double total = 0;

    // Sumar semanas pendientes
    for (final semana in widget.semanas) {
      if (!semana.pagada) {
        total += semana.monto - semana.montoPagado;
      }
    }

    // Sumar costos adicionales pendientes
    for (final costo in widget.costosAdicionales) {
      if (!costo.pagado) {
        total += costo.monto - costo.montoPagado;
      }
    }

    return total;
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildAmountSection(),
            const SizedBox(height: 24),
            _buildPaymentMethodSection(),
            const SizedBox(height: 24),
            if (widget.costosAdicionales.isNotEmpty) ...[
              _buildCostosAdicionalesSection(),
              const SizedBox(height: 24),
            ],
            if (_currentDistribution != null) ...[
              _buildDistributionSection(),
              const SizedBox(height: 24),
            ],
            _buildNotesSection(),
            const SizedBox(height: 32),
            _buildGenerateButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildAmountSection() {
    final totalPendiente = _calculateTotalPendiente();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payments,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Monto a Pagar',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _montoController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
            ],
            style: GoogleFonts.poppins(
              fontSize: 24,
              fontWeight: FontWeight.w600,
              color: AppStyles.textColor,
            ),
            decoration: InputDecoration(
              prefixText: '\$ ',
              prefixStyle: GoogleFonts.poppins(
                fontSize: 24,
                fontWeight: FontWeight.w600,
                color: AppStyles.primaryColor,
              ),
              hintText: '0.00',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Ingrese un monto válido';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'El monto debe ser mayor a cero';
              }
              return null;
            },
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withValues(alpha: 0.05),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Total Pendiente:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${totalPendiente.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.primaryColor,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          _buildQuickSuggestions(),
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    final suggestions = <double>[];

    // Próxima semana
    if (widget.montoPorSemana > 0) {
      suggestions.add(widget.montoPorSemana);
      suggestions.add(widget.montoPorSemana * 2);
      suggestions.add(widget.montoPorSemana * 4);
    }

    // Montos redondos
    suggestions.addAll([500.0, 1000.0, 2000.0]);

    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: suggestions.take(6).map((amount) {
        String label = '\$${amount.toStringAsFixed(0)}';
        if (amount == widget.montoPorSemana) {
          label += ' (1 sem)';
        } else if (amount == widget.montoPorSemana * 2) {
          label += ' (2 sem)';
        } else if (amount == widget.montoPorSemana * 4) {
          label += ' (4 sem)';
        }

        return GestureDetector(
          onTap: () {
            _montoController.text = amount.toStringAsFixed(2);
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppStyles.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Text(
              label,
              style: GoogleFonts.poppins(
                fontSize: 11,
                fontWeight: FontWeight.w500,
                color: AppStyles.primaryColor,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.payment,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Método de Pago',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          DropdownButtonFormField<String>(
            value: _metodoPago,
            decoration: InputDecoration(
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
            items: _metodosPago.map((metodo) {
              return DropdownMenuItem<String>(
                value: metodo,
                child: Text(metodo),
              );
            }).toList(),
            onChanged: (value) {
              setState(() {
                _metodoPago = value!;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCostosAdicionalesSection() {
    final costosPendientes = widget.costosAdicionales
        .where((costo) => !costo.pagado)
        .toList();

    if (costosPendientes.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.add_circle_outline,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Costos Adicionales',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Selecciona los costos que deseas pagar primero',
            style: GoogleFonts.poppins(
              fontSize: 14,
              color: AppStyles.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 16),
          ...costosPendientes.map((costo) => _buildCostoToggle(costo)),
        ],
      ),
    );
  }

  Widget _buildCostoToggle(CostoAdicional costo) {
    final pendiente = costo.monto - costo.montoPagado;
    final isSelected = _costosSeleccionados.contains(costo.id);

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected ? AppStyles.primaryColor.withValues(alpha: 0.1) : Colors.grey[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected ? AppStyles.primaryColor : Colors.grey[300]!,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Checkbox(
                value: isSelected,
                onChanged: (value) => _onCostoToggled(costo.id, value ?? false),
                activeColor: AppStyles.primaryColor,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      costo.nombre,
                      style: GoogleFonts.poppins(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppStyles.textColor,
                      ),
                    ),
                    if (costo.descripcion.isNotEmpty) ...[
                      const SizedBox(height: 4),
                      Text(
                        costo.descripcion,
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          color: AppStyles.secondaryTextColor,
                        ),
                      ),
                    ],
                    const SizedBox(height: 8),
                    Text(
                      'Pendiente: \$${pendiente.toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Colors.orange[700],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          // Input de monto cuando está seleccionado
          if (isSelected) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Monto a abonar:',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppStyles.textColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextFormField(
                        controller: _costoControllers[costo.id],
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(RegExp(r'^\d*\.?\d{0,2}')),
                        ],
                        style: GoogleFonts.poppins(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: AppStyles.textColor,
                        ),
                        decoration: InputDecoration(
                          prefixText: '\$ ',
                          prefixStyle: GoogleFonts.poppins(
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: AppStyles.primaryColor,
                          ),
                          hintText: '0.00',
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
                          ),
                          contentPadding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 12),
                // Botones de monto rápido
                Column(
                  children: [
                    const SizedBox(height: 20),
                    _buildQuickAmountButton(
                      label: 'Todo',
                      onTap: () {
                        _costoControllers[costo.id]?.text = pendiente.toStringAsFixed(2);
                        _onCostoAmountChanged(costo.id);
                      },
                    ),
                    const SizedBox(height: 8),
                    _buildQuickAmountButton(
                      label: '50%',
                      onTap: () {
                        final amount = pendiente * 0.5;
                        _costoControllers[costo.id]?.text = amount.toStringAsFixed(2);
                        _onCostoAmountChanged(costo.id);
                      },
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              'Máximo: \$${pendiente.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 12,
                color: AppStyles.secondaryTextColor,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickAmountButton({
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: AppStyles.primaryColor.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: AppStyles.primaryColor.withValues(alpha: 0.3),
          ),
        ),
        child: Text(
          label,
          style: GoogleFonts.poppins(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: AppStyles.primaryColor,
          ),
        ),
      ),
    );
  }

  Widget _buildDistributionSection() {
    if (_currentDistribution == null) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance_wallet,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Distribución del Pago',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Resumen
          _buildDistributionSummary(),

          const SizedBox(height: 16),

          // Detalles de costos adicionales
          if (_currentDistribution!.costDistributions.isNotEmpty) ...[
            _buildCostDistributionDetails(),
            const SizedBox(height: 16),
          ],

          // Detalles de semanas
          if (_currentDistribution!.weekDistributions.isNotEmpty) ...[
            _buildWeekDistributionDetails(),
          ],
        ],
      ),
    );
  }

  Widget _buildDistributionSummary() {
    final distribution = _currentDistribution!;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppStyles.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Monto Total:',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
              Text(
                '\$${distribution.totalAmount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: AppStyles.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distribuido:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
              Text(
                '\$${distribution.totalDistributed.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          if (distribution.remainingAmount > 0.01) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Restante:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${distribution.remainingAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCostDistributionDetails() {
    final costDistributions = _currentDistribution!.costDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Costos Adicionales (${costDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...costDistributions.map((cost) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                cost.isFullyPaid ? Icons.check_circle : Icons.schedule,
                size: 16,
                color: cost.isFullyPaid ? Colors.green[700] : Colors.blue[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  cost.costoAdicional.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                '\$${cost.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildWeekDistributionDetails() {
    final weekDistributions = _currentDistribution!.weekDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas (${weekDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: weekDistributions.map((week) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: week.isExisting ? Colors.orange[100] : Colors.green[100],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: week.isExisting ? Colors.orange[300]! : Colors.green[300]!,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  week.isExisting ? Icons.schedule : Icons.add_circle,
                  size: 14,
                  color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                ),
                const SizedBox(width: 4),
                Text(
                  'S${week.numeroSemana}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '\$${week.amount.toStringAsFixed(0)}',
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: week.isExisting ? Colors.orange[600] : Colors.green[600],
                  ),
                ),
              ],
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildNotesSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.note_add,
                color: AppStyles.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Notas (Opcional)',
                style: GoogleFonts.poppins(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _notasController,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: 'Agregar notas o comentarios sobre este pago...',
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppStyles.primaryColor, width: 2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGenerateButton() {
    final canGenerate = _formKey.currentState?.validate() ?? false;
    final hasAmount = (double.tryParse(_montoController.text) ?? 0) > 0;
    final hasDistribution = _currentDistribution != null &&
                           _currentDistribution!.totalDistributed > 0;

    return SizedBox(
      width: double.infinity,
      height: 56,
      child: ElevatedButton(
        onPressed: (canGenerate && hasAmount && hasDistribution)
            ? _generateRecibo
            : null,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppStyles.primaryColor,
          foregroundColor: Colors.white,
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          disabledBackgroundColor: Colors.grey[300],
          disabledForegroundColor: Colors.grey[600],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.receipt_long, size: 24),
            const SizedBox(width: 12),
            Text(
              'Generar Recibo',
              style: GoogleFonts.poppins(
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _generateRecibo() {
    if (!_formKey.currentState!.validate()) return;
    if (_currentDistribution == null) return;

    final amount = double.tryParse(_montoController.text) ?? 0;
    if (amount <= 0) return;

    // Crear los detalles del pago
    final detalles = _currentDistribution!.toDetallesPago(widget.alumno.id);

    // Crear el concepto del recibo
    String concepto = 'Pago de ';
    final conceptos = <String>[];

    if (_currentDistribution!.weekDistributions.isNotEmpty) {
      final semanas = _currentDistribution!.weekDistributions.length;
      conceptos.add('$semanas semana${semanas > 1 ? 's' : ''}');
    }

    if (_currentDistribution!.costDistributions.isNotEmpty) {
      final costos = _currentDistribution!.costDistributions.length;
      conceptos.add('$costos costo${costos > 1 ? 's' : ''} adicional${costos > 1 ? 'es' : ''}');
    }

    concepto += conceptos.join(' y ');

    // Agregar notas si las hay
    final notas = _notasController.text.trim();
    if (notas.isNotEmpty) {
      concepto += ' - $notas';
    }

    // Preparar los datos para el callback
    final reciboData = {
      'alumno': widget.alumno,
      'monto': amount,
      'metodoPago': _metodoPago,
      'concepto': concepto,
      'detalles': detalles,
      'distribution': _currentDistribution,
      'notas': notas,
    };

    // Llamar al callback
    widget.onGenerarRecibo(reciboData);
  }
}
