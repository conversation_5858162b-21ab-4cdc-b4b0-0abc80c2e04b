import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../domain/entities/alumno.dart';
import '../../../../../../styles/app_styles.dart';
import '../../controllers/simple_payment_controller.dart';
import 'widgets/student_header_widget.dart';
import 'widgets/amount_section_widget.dart';
import 'widgets/payment_method_section_widget.dart';
import 'widgets/additional_costs_section_widget.dart';
import 'widgets/distribution_section_widget.dart';
import 'widgets/notes_section_widget.dart';
import 'widgets/generate_button_widget.dart';

class SimplePaymentForm extends StatelessWidget {
  final Alumno alumno;
  final VoidCallback? onReciboCreated;

  const SimplePaymentForm({
    super.key,
    required this.alumno,
    this.onReciboCreated,
  });

  @override
  Widget build(BuildContext context) {
    // Inicializar el controlador
    final controller = Get.put(SimplePaymentController(), tag: alumno.id);
    controller.initialize(alumno, onReciboCreated: onReciboCreated);

    return Scaffold(
      body: Obx(() {
        if (controller.isLoading.value) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Header con información del alumno
              StudentHeaderWidget(alumno: alumno),

              const SizedBox(height: 24),

              // Formulario de pago
              Expanded(
                child: Form(
                  key: controller.formKey,
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        AmountSectionWidget(),
                        const SizedBox(height: 24),
                        PaymentMethodSectionWidget(),
                        const SizedBox(height: 24),
                        Obx(() {
                          if (controller.costosAdicionales.isNotEmpty) {
                            return Column(
                              children: [
                                AdditionalCostsSectionWidget(),
                                const SizedBox(height: 24),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        }),
                        Obx(() {
                          if (controller.currentDistribution.value != null) {
                            return Column(
                              children: [
                                DistributionSectionWidget(),
                                const SizedBox(height: 24),
                              ],
                            );
                          }
                          return const SizedBox.shrink();
                        }),
                        NotesSectionWidget(),
                        const SizedBox(height: 32),
                        GenerateButtonWidget(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      }),
    );
  }
}





