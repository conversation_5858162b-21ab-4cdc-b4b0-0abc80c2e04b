import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../../../../../styles/app_styles.dart';
import '../../../controllers/simple_payment_controller.dart';

class DistributionSectionWidget extends StatelessWidget {
  const DistributionSectionWidget({super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Get.find<SimplePaymentController>();

    return Obx(() {
      final distribution = controller.currentDistribution.value;
      if (distribution == null) return const SizedBox.shrink();

      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: AppStyles.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'Distribución del Pago',
                  style: GoogleFonts.poppins(
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                    color: AppStyles.textColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Resumen
            _buildDistributionSummary(distribution),

            const SizedBox(height: 16),

            // Detalles de costos adicionales
            if (distribution.costDistributions.isNotEmpty) ...[
              _buildCostDistributionDetails(distribution),
              const SizedBox(height: 16),
            ],

            // Detalles de semanas
            if (distribution.weekDistributions.isNotEmpty) ...[
              _buildWeekDistributionDetails(distribution),
            ],
          ],
        ),
      );
    });
  }

  Widget _buildDistributionSummary(distribution) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppStyles.primaryColor.withValues(alpha: 0.05),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Monto Total:',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppStyles.textColor,
                ),
              ),
              Text(
                '\$${distribution.totalAmount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 16,
                  fontWeight: FontWeight.w700,
                  color: AppStyles.primaryColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Distribuido:',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  color: AppStyles.secondaryTextColor,
                ),
              ),
              Text(
                '\$${distribution.totalDistributed.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.green[700],
                ),
              ),
            ],
          ),
          if (distribution.remainingAmount > 0.01) ...[
            const SizedBox(height: 8),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Restante:',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    color: AppStyles.secondaryTextColor,
                  ),
                ),
                Text(
                  '\$${distribution.remainingAmount.toStringAsFixed(2)}',
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Colors.orange[700],
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCostDistributionDetails(distribution) {
    final costDistributions = distribution.costDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Costos Adicionales (${costDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        ...costDistributions.map((cost) => Container(
          margin: const EdgeInsets.only(bottom: 8),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.blue[50],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                cost.isFullyPaid ? Icons.check_circle : Icons.schedule,
                size: 16,
                color: cost.isFullyPaid ? Colors.green[700] : Colors.blue[700],
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  cost.costoAdicional.nombre,
                  style: GoogleFonts.poppins(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                    color: AppStyles.textColor,
                  ),
                ),
              ),
              Text(
                '\$${cost.amount.toStringAsFixed(2)}',
                style: GoogleFonts.poppins(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
        )),
      ],
    );
  }

  Widget _buildWeekDistributionDetails(distribution) {
    final weekDistributions = distribution.weekDistributions;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Semanas (${weekDistributions.length})',
          style: GoogleFonts.poppins(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            color: AppStyles.textColor,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: weekDistributions.map((week) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: week.isExisting ? Colors.orange[100] : Colors.green[100],
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: week.isExisting ? Colors.orange[300]! : Colors.green[300]!,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  week.isExisting ? Icons.schedule : Icons.add_circle,
                  size: 14,
                  color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                ),
                const SizedBox(width: 4),
                Text(
                  'S${week.numeroSemana}',
                  style: GoogleFonts.poppins(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: week.isExisting ? Colors.orange[700] : Colors.green[700],
                  ),
                ),
                const SizedBox(width: 4),
                Text(
                  '\$${week.amount.toStringAsFixed(0)}',
                  style: GoogleFonts.poppins(
                    fontSize: 11,
                    fontWeight: FontWeight.w500,
                    color: week.isExisting ? Colors.orange[600] : Colors.green[600],
                  ),
                ),
              ],
            ),
          )).toList(),
        ),
      ],
    );
  }
}
